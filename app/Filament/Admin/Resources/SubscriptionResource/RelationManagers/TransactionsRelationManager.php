<?php

namespace App\Filament\Admin\Resources\SubscriptionResource\RelationManagers;

use App\Constants\TransactionStatus;
use App\Mapper\TransactionStatusMapper;
use App\Models\Transaction;
use App\Services\InvoiceService;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';

    public function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading(__('Invoices & Receipts'))
            ->recordTitleAttribute('invoice_number')
            ->columns([


                Tables\Columns\TextColumn::make('invoice_number')
                    ->label(__('Invoice'))
                    ->html()
                    ->searchable(['invoice_number', 'payment_provider_reference'])
                    ->formatStateUsing(function (Transaction $record) {
                        $html = '<div class="space-y-1">';
                        $html .= '<div class="font-medium">' . ($record->invoice_number ?? '-') . '</div>';
                        if ($record->payment_provider_reference) {
                            $html .= '<div class="text-xs text-gray-500 italic">' . $record->payment_provider_reference . '</div>';
                        }
                        $html .= '</div>';
                        return $html;
                    })
                    ->copyable()
                    ->copyableState(fn (Transaction $record) => $record->invoice_number ?? '')
                    ->copyMessage(__('Invoice number copied'))
                    ->copyMessageDuration(1500),
                Tables\Columns\TextColumn::make('subscription.plan.name')
                    ->label(__('Subscription'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label(__('Amount'))
                    ->formatStateUsing(function (string $state, Transaction $record) {
                        return money(abs($state), $record->currency->code);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (Transaction $record, TransactionStatusMapper $mapper): string => $mapper->mapColor($record->status))
                    ->formatStateUsing(fn (string $state, TransactionStatusMapper $mapper): string => $mapper->mapForDisplay($state))
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Date'))
                    ->dateTime(config('app.datetime_format'))
                    ->sortable()
                    ->searchable(),


            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options([
                        TransactionStatus::SUCCESS->value => __('Success'),
                        TransactionStatus::FAILED->value => __('Failed'),
                        TransactionStatus::PENDING->value => __('Pending'),
                        TransactionStatus::REFUNDED->value => __('Refunded'),
                        TransactionStatus::DISPUTED->value => __('Disputed'),
                    ]),
            ])
            ->headerActions([])
            ->actions([
                Tables\Actions\Action::make('view-invoice')
                    ->label(__('View Invoice'))
                    ->icon('heroicon-o-document-text')
                    ->color('primary')
                    ->button()
                    ->outlined()
                    ->visible(fn (Transaction $record, InvoiceService $invoiceService): bool => $invoiceService->canGenerateInvoices($record))
                    ->url(
                        fn (Transaction $record): string => route('invoice_receipt.generate', ['uuid' => $record->uuid, 'docType' => 'invoice']),
                        shouldOpenInNewTab: true
                    ),

                Tables\Actions\Action::make('view-receipts')
                    ->label(__('View Receipts'))
                    ->icon('heroicon-o-receipt-percent')
                    ->color('success')
                    ->button()
                    ->outlined()
                    ->visible(fn (Transaction $record): bool => $record->receipts()->count() > 0)
                    ->modalHeading(fn (Transaction $record) => __('Receipts for Invoice :number', ['number' => $record->invoice_number]))
                    ->modalContent(fn (Transaction $record) => view('filament.admin.modals.transaction-receipts', ['transaction' => $record]))
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel(__('Close')),
            ])
            ->bulkActions([])
            ->modifyQueryUsing(fn (Builder $query) => $query->with([
                'currency',
                'paymentProvider',
                'receipts',
                'subscription.plan',
            ]))
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No transactions found'))
            ->emptyStateDescription(__('This subscription has no associated transactions yet.'))
            ->emptyStateIcon('heroicon-o-document-text');
    }
}
